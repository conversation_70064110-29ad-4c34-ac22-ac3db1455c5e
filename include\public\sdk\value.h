#pragma once

#include "cameraDefines.h"
namespace percipio {
class Value {
 public:
  Value();
  Value(const Value&);
  Value(Value&&) noexcept;

  ~Value();

  Value& operator=(const Value&);
  Value& operator=(Value&&) noexcept;

  explicit Value(bool val);

  explicit Value(int8_t value);
  explicit Value(uint8_t val);

  explicit Value(int16_t value);
  explicit Value(uint16_t val);

  explicit Value(int32_t value);
  explicit Value(uint32_t val);

  explicit Value(int64_t value);
  explicit Value(uint64_t val);

  explicit Value(float val);
  explicit Value(double val);

  explicit Value(const std::string& val);

public:
  ValueType GetType() const noexcept;

  bool IsUndefined() const noexcept;

  bool IsBool() const noexcept;

  bool IsInt8() const noexcept;
  bool IsUInt8() const noexcept;

  bool IsInt16() const noexcept;
  bool IsUInt16() const noexcept;

  bool IsInt32() const noexcept;
  bool IsUInt32() const noexcept;

  bool IsInt64() const noexcept;
  bool IsUInt64() const noexcept;

  bool IsFloat() const noexcept;
  bool IsDouble() const noexcept;

  bool IsString() const noexcept;

  bool IsArray() const noexcept;
  bool IsDictionary() const noexcept;

 public:
  // unsafe convert ,  must sure ValueType is match
  operator bool() const noexcept;
  operator int8_t() const noexcept;
  operator uint8_t() const noexcept;

  operator int16_t() const noexcept;
  operator uint16_t() const noexcept;

  operator int32_t() const noexcept;
  operator uint32_t() const noexcept;

  operator int64_t() const noexcept;
  operator uint64_t() const noexcept;

  operator float() const noexcept;
  operator double() const noexcept;

  operator std::string() const;
  std::vector<Value> GetArray() const;
  std::vector<std::pair<std::string, Value>> GetDictionary() const;

 private:
  ValueType type_;

 protected:
  union Data {
    bool bool_value_;
    int8_t int8_value_;
    uint8_t uint8_value_;
    int16_t int16_value_;
    uint16_t uint16_value_;
    int32_t int32_value_;
    uint32_t uint32_value_;
    int64_t int64_value_;
    uint64_t uint64_value_;
    float float32_value_;
    double float64_value_;
  } data_;
  std::string string_value_;
};

}  // namespace percipio
