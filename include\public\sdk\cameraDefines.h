#pragma once
#include "cameraApiStatus.h"
#include <string>
#include <vector>
#include <functional>

namespace ucv {
namespace media {
class FrameSet;
class Frame;
class CameraCapture;
class FeatureImpl;
}  // namespace media
}  // namespace ucv
using UcvFrameSet = ucv::media::FrameSet;
using UcvFrame = ucv::media::Frame;
namespace percipio {
	namespace SensorType {
		const char* const DEPTH = "Depth";
		const char* const LEFT = "Left";
		const char* const RIGHT = "Right";
		const char* const COLOR = "Color";
	}

	enum class CameraState : int32_t {
        NotFound,
		Occupied,
		Opened,
		Closed,
		Capturing,
        Offlined,
        Error,
	};

	enum class InterfaceType : int32_t {
		Unknown = 0,
		USB = 1,
		Network = 2,
	};

	struct UsbInfo {
		int32_t bus;
		int32_t address;
	};

	struct NetworkInfo {
		std::string mac;
		std::string ip;
		std::string netmask;
		std::string gateway;
		std::string broadcast;
	};

	struct InterfaceInfo {
		InterfaceType interface_type;
		std::string name;
		std::string id;
		NetworkInfo network_info;
	};

	struct CameraInfo {
		// 接口信息
		InterfaceInfo interface_info;
		// 是网络设备时，network_info信息才有意义
		NetworkInfo network_info;
		// 是USB设备时，usb_info信息才有意义
		UsbInfo usb_info;

		// 相机自身信息
		std::string serial_number;
		std::string name;
		std::string model;
		std::string vendor;
		std::string firmware_version;

        CameraState state = CameraState::Closed;
	};

	struct UserSet {
	public:
		// 例如："Default0-7" "UserSet0-7"。对应 dataModel 中的 UserSet::name
		std::string sid;
         // 对应 dataModel 中的 description
		std::string name;
	};

	struct CameraIntrinsic {
		float data[3 * 3];
	};

	struct CameraExtrinsic {
		float data[4 * 4];
	};

	struct CameraDistortion {
		float data[12];
	};

	struct CameraRotation {
		float data[9];
	};

	struct CalibInfo {
		CameraIntrinsic intrinsic;
		CameraExtrinsic extrinsic;
		CameraDistortion distortion;
		//enum LensType,
	};

enum class RawPixelFormat : int32_t {
          Undefined = 0,
          Mono,

          Bayer8GB,
          Bayer8BG,
          Bayer8GR,
          Bayer8RG,

          Bayer8GRBG,
          Bayer8RGGB,
          Bayer8GBRG,
          Bayer8BGGR,

          CSIMono10,

          CSIBayer10GRBG,
          CSIBayer10RGGB,
          CSIBayer10GBRG,
          CSIBayer10BGGR,

          CSIMono12,
          CSIBayer12GRBG,
          CSIBayer12RGGB,
          CSIBayer12GBRG,

          CSIBayer12BGGR,

          Depth16,
          YVYU,
          YUYV,
          Mono16,

          TOFIRMono16,

          RGB,
          BGR,
          JPEG,
          MJPG,

          RGB48,
          BGR48,
          XYZ48,

          // Ty Camera IMU
          IMU,

          // genicam
          Mono8,
          BayerGB8,
          BayerBG8,
          BayerGR8,
          BayerRG8,
          Mono10p,
          CSIMono10P,
          BayerGB10p,
          CSIBayerGB10P,
          BayerBG10p,
          CSIBayerBG10P,
          BayerGR10p,
          CSIBayerGR10P,
          BayerRG10p,
          CSIBayerRG10P,
          Mono12p,
          CSIMono12P,
          BayerGB12p,
          CSIBayerGB12P,
          BayerBG12p,
          CSIBayerBG12P,
          BayerGR12p,
          CSIBayerGR12P,
          BayerRG12p,
          CSIBayerRG12P,
          CSIMono14P,
          CSIBayerGB14P,
          CSIBayerBG14P,
          CSIBayerGR14P,
          CSIBayerRG14P,
          BayerGB16,
          BayerBG16,
          BayerGR16,
          BayerRG16,
          RGB8,
          BGR8,
          YUV422_8,
          YUV422_8_UYVY,
          YCbCr420_8_YY_CbCr_Planar,
          YCbCr420_8_YY_CrCb_Planar,
          YCbCr420_8_YY_CbCr_Semiplanar,
          YCbCr420_8_YY_CrCb_Semiplanar,
          Coord3D_C16,
          Coord3D_ABC16,
          Coord3D_ABC32f,
          TofIR_FourGroup_Mono16,
        };


	enum class PixelFormat :uint16_t {
		UNDEFINED,
		DEPTH16,
		DEPTH32F,
		PointCloud,
		GRAY8,
		GRAY16,
		GRAY32F,
		RGB24,
	};

	struct ImageMode {
		int width;
		int height;
        RawPixelFormat pixel_format;
	};

	class Image {
	public:
        ~Image();
        bool IsValid();

        int32_t width();
        int32_t height();
		PixelFormat pixel_format();
		const uint8_t* data() const;

		uint64_t time_stamp();
		int64_t frame_index();
		std::string sensor();
		CalibInfo calib_info();
		float scale_unit();
    private:
        Image(UcvFrame* frame=nullptr);
        UcvFrame *frame_;
        friend class FrameSet;
	};

	class FrameSet {
	public:
       Image GetImage(const std::string& sensor_name) const;
       ~FrameSet();

	private:
        FrameSet(UcvFrameSet* frame_set_impl);
        UcvFrameSet* frame_set_impl_;

        friend class Camera;
	};

	enum class CameraEventCode : int32_t {
		Closed,
		Opened,
		Started,
		Stopped,
		Offlined,
		Error,
	};
	
	std::string CameraEventCodeToString(CameraEventCode code);

	using CameraEventCallback = std::function<void(CameraEventCode event_code, int error_code)>;

	enum class AccessMode : int32_t {
          NotAvailable = 0,
          Readable = 1,
          Writable = 2,
          ReadWritable = 3,
        };

	enum class FeatureType : int32_t {
        Undefined,
        Bool,
        Int64,
		Float64,
        Enumeration,
		String,
        ByteArray,
        Dictionary,
	};

    enum class ValueType : int32_t {
        Undefined,

        Bool,

        Int8,
        UInt8,

        Int16,
        UInt16,

        Int32,
        UInt32,

        Int64,
        UInt64,

        Float32,
        Float64,

        String,

        Array,

        Dictionary,
    };

	template <typename T>
    struct NumericRange {
      T minValue;
      T maxValue;
      T step;
    };
	using Int64Range = NumericRange<int64_t>;
    using Float64Range = NumericRange<double>;


	struct EnumItem {
      std::string name;
      int32_t value;
    };
}

