#pragma once

#include <string>

#define CAMERA_API_VERSION_MAJOR       1
#define CAMERA_API_VERSION_MINOR       0
#define CAMERA_API_VERSION_PATCH       0


int GetVersionMajor();
int GetVersionMinor();
int GetVersionPatch();
std::string GetVersionString();

int GetTYLibVersionMajor();
int GetTYLibVersionMinor();
int GetTYLibVersionPatch();
std::string GetTYLibVersionString();

int GetVCameraVersionMajor();
int GetVCameraVersionMinor();
int GetVCameraVersionPatch();
std::string GetVCameraVersionString();
