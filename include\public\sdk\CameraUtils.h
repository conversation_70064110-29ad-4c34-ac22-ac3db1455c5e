#pragma once

#include "cameraApiExport.h"
#include "cameraDefines.h"

#include <string>

namespace percipio {
	class CAMERA_API_EXPORT CameraUtils {
	public:
		static void Init();
		static std::vector<CameraInfo> DiscoverCameras();

		/**
		 * @brief Set camera ip address.
		 * 
		 * \param camera_info camera info of the camera
		 * \param ip new ip address
		 * \param mask new subnet mask
		 * \param gateway new gateway
		 * \return 
		 */
		static CameraApiStatus SetIpAddress(
			const CameraInfo& camera_info,
			const std::string& ip,
			const std::string& mask = "",
			const std::string& gateway = "");

		/**
		 * @brief Set camera ip address.
		 * 
		 * \param mac MAC address of the camera
		 * \param ip new ip address
		 * \param mask new subnet mask
		 * \param gateway new gateway
		 * \return 
		 */
		static CameraApiStatus SetIpAddress(
			const std::string& mac,
			const std::string& ip,
			const std::string& mask = "",
			const std::string& gateway = "");

		/**
		 * Set camera ip address to dynamic.
		 * 
		 * \param camera_info camera info of the camera
		 * \return 
		 */
		static CameraApiStatus SetIpToDynamic(const CameraInfo& camera_info);

		/**
		 * Set camera ip address to dynamic.
		 * 
		 * \param mac MAC address of the camera
		 * \return 
		 */
		static CameraApiStatus SetIpToDynamic(const std::string& mac);

	private:
		CameraUtils() = delete;
		~CameraUtils() = delete;
		CameraUtils(const CameraUtils&) = delete;
		CameraUtils& operator=(const CameraUtils&) = delete;
	};
}
