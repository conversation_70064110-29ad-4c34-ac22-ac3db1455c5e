#pragma once

#include "cameraApiExport.h"
#include "cameraApiStatus.h"
#include "cameraDefines.h"
#include "feature.h"
#include "CameraUtils.h"
#include "userSetManager.h"

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace percipio {

/**
 * @class Camera
 * @brief Main camera control class for the Percipio SDK
 *
 * The Camera class provides a high-level interface for controlling camera devices.
 * It supports camera connection/disconnection, capture control, feature management,
 * sensor configuration, and callback registration for frame and event handling.
 */
class CAMERA_API_EXPORT Camera {
 public:
  /**
   * @brief Default constructor
   * Creates an uninitialized camera object
   */
  Camera();

  /**
   * @brief Copy constructor
   */
  Camera(const Camera& other);

  /**
   * @brief Copy assignment operator
   */
  Camera& operator=(const Camera& other);

  /**
   * @brief Destructor
   * Cleans up camera resources and disconnects if connected
   */
  ~Camera();

  // Move operations are explicitly deleted to prevent resource management issues
  Camera(Camera&& other) = delete;
  Camera& operator=(Camera&& other) = delete;

  /**
   * @brief Retrieves camera information
   * @param camera_info Reference to CameraInfo structure to be filled
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus GetCameraInfo(CameraInfo& camera_info) const;

  /**
   * @brief Connects to the camera
   * @param session_key Optional session key for camera access (default: empty)
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus Connect(std::string session_key = "");

  /**
   * @brief Disconnects from the camera
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus Disconnect();

  /**
   * @brief Gets the current camera state
   * @param camera_status Reference to CameraState to be filled with current status
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus GetCameraState(CameraState& camera_status);

  // === Capture Control Methods ===

  /**
   * @brief Starts image capture
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus StartCapture();

  /**
   * @brief Stops image capture
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus StopCapture();

  // === Feature Management Methods ===

  /**
   * @brief Retrieves a camera feature by name
   * @param feature_name Name of the feature to retrieve
   * @param feature Reference to Feature object to be filled
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus GetFeature(const std::string& feature_name, Feature& feature);

  /**
   * @brief Triggers a software trigger event
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus FireSoftwareTrigger();

  // === Image Mode and Sensor Configuration Methods ===

  /**
   * @brief Gets available image modes for a specific sensor
   * @param sensor_name Name of the sensor
   * @param resolutions Vector to be filled with available ImageMode options
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus GetImageModes(std::string sensor_name, std::vector<ImageMode>& resolutions);

  /**
   * @brief Gets the current image mode for a specific sensor
   * @param sensor_name Name of the sensor
   * @param image_mode Reference to ImageMode to be filled with current mode
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus GetCurrentImageMode(std::string sensor_name, ImageMode& image_mode);

  /**
   * @brief Sets the image mode for a specific sensor
   * @param sensor_name Name of the sensor
   * @param mode ImageMode to set
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus SetImageMode(std::string sensor_name, const ImageMode& mode);

  /**
   * @brief Checks if a specific sensor exists on the camera
   * @param sensor_name Name of the sensor to check
   * @param has_sensor Reference to bool that will be set to true if sensor exists
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus HasSensor(std::string sensor_name, bool& has_sensor);

  /**
   * @brief Checks if a specific sensor is enabled
   * @param sensor_name Name of the sensor to check
   * @param is_enabled Reference to bool that will be set to true if sensor is enabled
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus IsSensorEnabled(std::string sensor_name, bool& is_enabled);

  /**
   * @brief Enables or disables a specific sensor
   * @param sensor_name Name of the sensor to configure
   * @param enable True to enable the sensor, false to disable
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus SetSensorEnabled(std::string sensor_name, bool enable);

  /**
   * @brief Enables or disables undistortion for a specific sensor
   * @param sensor_name Name of the sensor to configure
   * @param enable True to enable undistortion, false to disable
   * @return CameraApiStatus indicating success or failure
   */
  CameraApiStatus SetUndistortionEnabled(std::string sensor_name, bool enable);

  // === User Set Management ===

  /**
   * @brief Gets the user set manager for camera configuration management
   * @return Reference to the UserSetManager instance
   */
  UserSetManager& GetUserSetManager() { return user_set_manager_; }

  // === Callback Type Definitions ===

  /**
   * @brief Callback function type for frame set events
   * Called when a new frame set is available from the camera
   */
  using FrameSetCallback = std::function<void(const FrameSet& frame_set)>;

  /**
   * @brief Callback function type for camera feature changes
   * Called when camera features are modified
   */
  using FeaturesChangedCallback = std::function<void(const std::vector<Feature>&)>;

  // === Callback Registration Methods ===

  /**
   * @brief Registers a callback for frame set events
   * @param callback Function to be called when new frame sets are available
   */
  void RegisterFrameSetCallback(FrameSetCallback callback);

  /**
   * @brief Registers a callback for camera events
   * @param callback Function to be called when camera events occur
   */
  void RegisterCameraEventCallback(CameraEventCallback callback);

  /**
   * @brief Registers a callback for feature change events
   * @param callback Function to be called when camera features change
   */
  void RegisterFeaturesChangedCallback(FeaturesChangedCallback callback);

 private:

  friend class CameraFactory;
  /**
   * @brief Constructor with camera implementation
   * @param camera_impl Pointer to the underlying camera implementation
   */
  Camera(ucv::media::CameraCapture* camera_impl);

  ucv::media::CameraCapture* camera_impl_{nullptr};  ///< Pointer to underlying camera implementation
  UserSetManager user_set_manager_;                  ///< Manager for camera user sets and configurations

};

/**
 * @class CameraFactory
 * @brief Factory class for creating Camera instances
 *
 * The CameraFactory provides static methods to create Camera objects using
 * different identification methods such as serial number, IP address, or
 * camera information structure.
 */
class CAMERA_API_EXPORT CameraFactory {
 public:
  /**
   * @brief Creates a Camera instance using serial number
   * @param serial_number The serial number of the camera to connect to
   * @return Camera object configured for the specified device
   */
  static Camera GetCameraBySerialNumber(std::string serial_number);

  /**
   * @brief Creates a Camera instance using IP address
   * @param ip_address The IP address of the camera to connect to
   * @return Camera object configured for the specified device
   */
  static Camera GetCameraByIpAddress(std::string ip_address);

  /**
   * @brief Creates a Camera instance using camera information
   * @param camera_info CameraInfo structure containing camera details
   * @return Camera object configured for the specified device
   */
  static Camera GetCameraByCameraInfo(const CameraInfo& camera_info);
};

}  // namespace percipio
