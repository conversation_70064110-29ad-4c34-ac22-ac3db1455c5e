#pragma once

#include "cameraApiExport.h"
#include "cameraApiStatus.h"
#include "cameraDefines.h"
#include "feature.h"
#include "CameraUtils.h"
#include "userSetManager.h"

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace percipio {
class CAMERA_API_EXPORT Camera {
 public:
  Camera();
  Camera(const Camera& other);
  Camera& operator=(const Camera& other);
  Camera(ucv::media::CameraCapture* camera_impl);

  ~Camera();

  Camera(Camera&& other) = delete;
  Camera& operator=(Camera&& other) = delete;

  CameraApiStatus GetCameraInfo(CameraInfo& camera_info) const;
  CameraApiStatus Connect(std::string session_key = "");
  CameraApiStatus Disconnect();
  CameraApiStatus GetCameraState(CameraState& camera_status);

  CameraApiStatus StartCapture();
  CameraApiStatus StopCapture();

  CameraApiStatus GetFeature(const std::string& feature_name, Feature& feature);

  CameraApiStatus FireSoftwareTrigger();

  CameraApiStatus GetImageModes(std::string sensor_name, std::vector<ImageMode>& resolutions);
  CameraApiStatus GetCurrentImageMode(std::string sensor_name, ImageMode& image_mode);
  CameraApiStatus SetImageMode(std::string sensor_name, const ImageMode& mode);

  CameraApiStatus HasSensor(std::string sensor_name, bool& has_sensor);
  CameraApiStatus IsSensorEnabled(std::string sensor_name, bool& is_enabled);
  CameraApiStatus SetSensorEnabled(std::string sensor_name, bool enable);
  CameraApiStatus SetUndistortionEnabled(std::string sensor_name, bool enable);

  UserSetManager& GetUserSetManager() { return user_set_manager_; }

  using FrameSetCallback = std::function<void(const FrameSet& frame_set)>;
  using FeaturesChangedCallback = std::function<void(const std::vector<Feature>&)>;

  void RegisterFrameSetCallback(FrameSetCallback callback);
  void RegisterCameraEventCallback(CameraEventCallback callback);
  void RegisterFeaturesChangedCallback(FeaturesChangedCallback callback);

 private:
  ucv::media::CameraCapture* camera_impl_{nullptr};
  UserSetManager user_set_manager_;
};

class CAMERA_API_EXPORT CameraFactory {
 public:
  static Camera GetCameraBySerialNumber(std::string serial_number);
  static Camera GetCameraByIpAddress(std::string ip_address);
  static Camera GetCameraByCameraInfo(const CameraInfo& camera_info);
};
}  // namespace percipio
