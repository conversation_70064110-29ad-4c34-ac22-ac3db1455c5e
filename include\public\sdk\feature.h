#pragma once
#include "cameraApiExport.h"
#include "cameraDefines.h"
#include "value.h"

#include <memory>
#include <cstdint>
#include <string>

namespace ucv{
  namespace media {
    class FeatureImpl;
  }
}

namespace percipio {
class CAMERA_API_EXPORT Feature {
 public:
  Feature() = default;
  Feature(const Feature& other);
  Feature& operator=(const Feature& other);
  ~Feature();

  bool IsValid();
  std::string GetName();
  FeatureType GetType();
  CameraApiStatus GetAccessMode(AccessMode& mode);
  CameraApiStatus GetValue(Value& value);
  CameraApiStatus SetValue(const Value& value);
  CameraApiStatus GetRange(Int64Range& range);
  CameraApiStatus GetRange(Float64Range& range); 
  CameraApiStatus GetEnumItems(std::vector<EnumItem>& enum_items);

 private:
  Feature(ucv::media::FeatureImpl* feature_impl);
  ucv::media::FeatureImpl* impl_ = nullptr;

  friend class Camera;
};
}  // namespace percipio
