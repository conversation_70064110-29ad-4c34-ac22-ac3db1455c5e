#pragma once
#include "cameraApiExport.h"
#include "cameraApiStatus.h"
#include "cameraDefines.h"

#include <memory>
#include <vector>
#include <string>

namespace percipio {
    class CAMERA_API_EXPORT UserSetManager {
    public:
        explicit UserSetManager(ucv::media::CameraCapture* impl);
        UserSetManager& operator=(const UserSetManager& other);
       ~UserSetManager();
    public:
        CameraApiStatus GetAllUserSets(std::vector<UserSet>& user_sets);
        CameraApiStatus SaveToUserSet(const UserSet& user_set);
        CameraApiStatus GetUserSet(const std::string& user_set_name, UserSet& user_set);
        CameraApiStatus CurrentUserSet(std::string& name);
        CameraApiStatus LoadUserSet(const std::string& name);
        CameraApiStatus GetPowerOnUserSet(std::string& name);
        CameraApiStatus SetPowerOnUserSet(const std::string& name);

    private:
        ucv::media::CameraCapture* camera_impl_;
    };

}
